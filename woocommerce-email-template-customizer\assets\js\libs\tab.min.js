!function(E,O,w,k){"use strict";O=void 0!==O&&O.Math==Math?O:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),E.fn.tab=function(r){var l,d=E.isFunction(this)?E(O):E(this),u=d.selector||"",b=(new Date).getTime(),g=[],f=r,F="string"==typeof f,S=[].slice.call(arguments,1),j=!1;return d.each(function(){var h,o,p,m,v,y,T=E.isPlainObject(r)?E.extend(!0,{},E.fn.tab.settings,r):E.extend({},E.fn.tab.settings),L=T.className,x=T.metadata,t=T.selector,A=T.error,e="."+T.namespace,a="module-"+T.namespace,P=E(this),n={},C=!0,i=0,s=this,c=P.data(a);v={initialize:function(){v.debug("Initializing tab menu item",P),v.fix.callbacks(),v.determineTabs(),v.debug("Determining tabs",T.context,o),T.auto&&v.set.auto(),v.bind.events(),T.history&&!j&&(v.initializeHistory(),j=!0),v.instantiate()},instantiate:function(){v.verbose("Storing instance of module",v),c=v,P.data(a,v)},destroy:function(){v.debug("Destroying tabs",P),P.removeData(a).off(e)},bind:{events:function(){E.isWindow(s)||(v.debug("Attaching tab activation events to element",P),P.on("click"+e,v.event.click))}},determineTabs:function(){var e;"parent"===T.context?(0<P.closest(t.vi-ui).length?(e=P.closest(t.vi-ui),v.verbose("Using closest UI element as parent",e)):e=P,h=e.parent(),v.verbose("Determined parent element for creating context",h)):T.context?(h=E(T.context),v.verbose("Using selector for tab context",T.context,h)):h=E("body"),T.childrenOnly?(o=h.children(t.tabs),v.debug("Searching tab context children for tabs",h,o)):(o=h.find(t.tabs),v.debug("Searching tab context for tabs",h,o))},fix:{callbacks:function(){E.isPlainObject(r)&&(r.onTabLoad||r.onTabInit)&&(r.onTabLoad&&(r.onLoad=r.onTabLoad,delete r.onTabLoad,v.error(A.legacyLoad,r.onLoad)),r.onTabInit&&(r.onFirstLoad=r.onTabInit,delete r.onTabInit,v.error(A.legacyInit,r.onFirstLoad)),T=E.extend(!0,{},E.fn.tab.settings,r))}},initializeHistory:function(){if(v.debug("Initializing page state"),E.address===k)return v.error(A.state),!1;if("state"==T.historyType){if(v.debug("Using HTML5 to manage state"),!1===T.path)return v.error(A.path),!1;E.address.history(!0).state(T.path)}E.address.bind("change",v.event.history.change)},event:{click:function(e){var t=E(this).data(x.tab);t!==k?(T.history?(v.verbose("Updating page state",e),E.address.value(t)):(v.verbose("Changing tab",e),v.changeTab(t)),e.preventDefault()):v.debug("No tab specified")},history:{change:function(e){var t=e.pathNames.join("/")||v.get.initialPath(),a=T.templates.determineTitle(t)||!1;v.performance.display(),v.debug("History change event",t,e),y=e,t!==k&&v.changeTab(t),a&&E.address.title(a)}}},refresh:function(){p&&(v.debug("Refreshing tab",p),v.changeTab(p))},cache:{read:function(e){return e!==k&&n[e]},add:function(e,t){e=e||p,v.debug("Adding cached content for",e),n[e]=t},remove:function(e){e=e||p,v.debug("Removing cached content for",e),delete n[e]}},set:{auto:function(){var e="string"==typeof T.path?T.path.replace(/\/$/,"")+"/{$tab}":"/{$tab}";v.verbose("Setting up automatic tab retrieval from server",e),E.isPlainObject(T.apiSettings)?T.apiSettings.url=e:T.apiSettings={url:e}},loading:function(e){var t=v.get.tabElement(e);t.hasClass(L.loading)||(v.verbose("Setting loading state for",t),t.addClass(L.loading).siblings(o).removeClass(L.active+" "+L.loading),0<t.length&&T.onRequest.call(t[0],e))},state:function(e){E.address.value(e)}},changeTab:function(u){var b=O.history&&O.history.pushState&&T.ignoreFirstLoad&&C,g=T.auto||E.isPlainObject(T.apiSettings),f=g&&!b?v.utilities.pathToArray(u):v.get.defaultPathArray(u);u=v.utilities.arrayToPath(f),E.each(f,function(e,t){var a,n,i,o,r=f.slice(0,e+1),s=v.utilities.arrayToPath(r),c=v.is.tab(s),l=e+1==f.length,d=v.get.tabElement(s);if(v.verbose("Looking for tab",t),c){if(v.verbose("Tab was found",t),p=s,m=v.utilities.filterArray(f,r),l?o=!0:(n=f.slice(0,e+2),i=v.utilities.arrayToPath(n),(o=!v.is.tab(i))&&v.verbose("Tab parameters found",n)),o&&g)return b?(v.debug("Ignoring remote content on first tab load",s),C=!1,v.cache.add(u,d.html()),v.activate.all(s),T.onFirstLoad.call(d[0],s,m,y),T.onLoad.call(d[0],s,m,y)):(v.activate.navigation(s),v.fetch.content(s,u)),!1;v.debug("Opened local tab",s),v.activate.all(s),v.cache.read(s)||(v.cache.add(s,!0),v.debug("First time tab loaded calling tab init"),T.onFirstLoad.call(d[0],s,m,y)),T.onLoad.call(d[0],s,m,y)}else{if(-1!=u.search("/")||""===u)return v.error(A.missingTab,P,h,s),!1;if(s=(a=E("#"+u+', a[name="'+u+'"]')).closest("[data-tab]").data(x.tab),d=v.get.tabElement(s),a&&0<a.length&&s)return v.debug("Anchor link used, opening parent tab",d,a),d.hasClass(L.active)||setTimeout(function(){v.scrollTo(a)},0),v.activate.all(s),v.cache.read(s)||(v.cache.add(s,!0),v.debug("First time tab loaded calling tab init"),T.onFirstLoad.call(d[0],s,m,y)),T.onLoad.call(d[0],s,m,y),!1}})},scrollTo:function(e){var t=!!(e&&0<e.length)&&e.offset().top;!1!==t&&(v.debug("Forcing scroll to an in-page link in a hidden tab",t,e),E(w).scrollTop(t))},update:{content:function(e,t,a){var n=v.get.tabElement(e),i=n[0];a=a!==k?a:T.evaluateScripts,"string"==typeof T.cacheType&&"dom"==T.cacheType.toLowerCase()&&"string"!=typeof t?n.empty().append(E(t).clone(!0)):a?(v.debug("Updating HTML and evaluating inline scripts",e,t),n.html(t)):(v.debug("Updating HTML",e,t),i.innerHTML=t)}},fetch:{content:function(t,a){var e,n,i=v.get.tabElement(t),o={dataType:"html",encodeParameters:!1,on:"now",cache:T.alwaysRefresh,headers:{"X-Remote":!0},onSuccess:function(e){"response"==T.cacheType&&v.cache.add(a,e),v.update.content(t,e),t==p?(v.debug("Content loaded",t),v.activate.tab(t)):v.debug("Content loaded in background",t),T.onFirstLoad.call(i[0],t,m,y),T.onLoad.call(i[0],t,m,y),T.loadOnce?v.cache.add(a,!0):"string"==typeof T.cacheType&&"dom"==T.cacheType.toLowerCase()&&0<i.children().length?setTimeout(function(){var e=i.children().clone(!0);e=e.not("script"),v.cache.add(a,e)},0):v.cache.add(a,i.html())},urlData:{tab:a}},r=i.api("get request")||!1,s=r&&"pending"===r.state();a=a||t,n=v.cache.read(a),T.cache&&n?(v.activate.tab(t),v.debug("Adding cached content",a),T.loadOnce||("once"==T.evaluateScripts?v.update.content(t,n,!1):v.update.content(t,n)),T.onLoad.call(i[0],t,m,y)):s?(v.set.loading(t),v.debug("Content is already loading",a)):E.api!==k?(e=E.extend(!0,{},T.apiSettings,o),v.debug("Retrieving remote content",a,e),v.set.loading(t),i.api(e)):v.error(A.api)}},activate:{all:function(e){v.activate.tab(e),v.activate.navigation(e)},tab:function(e){var t=v.get.tabElement(e),a="siblings"==T.deactivate?t.siblings(o):o.not(t),n=t.hasClass(L.active);v.verbose("Showing tab content for",t),n||(t.addClass(L.active),a.removeClass(L.active+" "+L.loading),0<t.length&&T.onVisible.call(t[0],e))},navigation:function(e){var t=v.get.navElement(e),a="siblings"==T.deactivate?t.siblings(d):d.not(t),n=t.hasClass(L.active);v.verbose("Activating tab navigation for",t,e),n||(t.addClass(L.active),a.removeClass(L.active+" "+L.loading))}},deactivate:{all:function(){v.deactivate.navigation(),v.deactivate.tabs()},navigation:function(){d.removeClass(L.active)},tabs:function(){o.removeClass(L.active+" "+L.loading)}},is:{tab:function(e){return e!==k&&0<v.get.tabElement(e).length}},get:{initialPath:function(){return d.eq(0).data(x.tab)||o.eq(0).data(x.tab)},path:function(){return E.address.value()},defaultPathArray:function(e){return v.utilities.pathToArray(v.get.defaultPath(e))},defaultPath:function(e){var t=d.filter("[data-"+x.tab+'^="'+e+'/"]').eq(0).data(x.tab)||!1;if(t){if(v.debug("Found default tab",t),i<T.maxDepth)return i++,v.get.defaultPath(t);v.error(A.recursion)}else v.debug("No default tabs found for",e,o);return i=0,e},navElement:function(e){return e=e||p,d.filter("[data-"+x.tab+'="'+e+'"]')},tabElement:function(e){var t,a,n,i;return e=e||p,n=v.utilities.pathToArray(e),i=v.utilities.last(n),t=o.filter("[data-"+x.tab+'="'+e+'"]'),a=o.filter("[data-"+x.tab+'="'+i+'"]'),0<t.length?t:a},tab:function(){return p}},utilities:{filterArray:function(e,t){return E.grep(e,function(e){return-1==E.inArray(e,t)})},last:function(e){return!!E.isArray(e)&&e[e.length-1]},pathToArray:function(e){return e===k&&(e=p),"string"==typeof e?e.split("/"):[e]},arrayToPath:function(e){return!!E.isArray(e)&&e.join("/")}},setting:function(e,t){if(v.debug("Changing setting",e,t),E.isPlainObject(e))E.extend(!0,T,e);else{if(t===k)return T[e];E.isPlainObject(T[e])?E.extend(!0,T[e],t):T[e]=t}},internal:function(e,t){if(E.isPlainObject(e))E.extend(!0,v,e);else{if(t===k)return v[e];v[e]=t}},debug:function(){!T.silent&&T.debug&&(T.performance?v.performance.log(arguments):(v.debug=Function.prototype.bind.call(console.info,console,T.name+":"),v.debug.apply(console,arguments)))},verbose:function(){!T.silent&&T.verbose&&T.debug&&(T.performance?v.performance.log(arguments):(v.verbose=Function.prototype.bind.call(console.info,console,T.name+":"),v.verbose.apply(console,arguments)))},error:function(){T.silent||(v.error=Function.prototype.bind.call(console.error,console,T.name+":"),v.error.apply(console,arguments))},performance:{log:function(e){var t,a;T.performance&&(a=(t=(new Date).getTime())-(b||t),b=t,g.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:s,"Execution Time":a})),clearTimeout(v.performance.timer),v.performance.timer=setTimeout(v.performance.display,500)},display:function(){var e=T.name+":",a=0;b=!1,clearTimeout(v.performance.timer),E.each(g,function(e,t){a+=t["Execution Time"]}),e+=" "+a+"ms",u&&(e+=" '"+u+"'"),(console.group!==k||console.table!==k)&&0<g.length&&(console.groupCollapsed(e),console.table?console.table(g):E.each(g,function(e,t){console.log(t.Name+": "+t["Execution Time"]+"ms")}),console.groupEnd()),g=[]}},invoke:function(n,e,t){var i,o,a,r=c;return e=e||S,t=s||t,"string"==typeof n&&r!==k&&(n=n.split(/[\. ]/),i=n.length-1,E.each(n,function(e,t){var a=e!=i?t+n[e+1].charAt(0).toUpperCase()+n[e+1].slice(1):n;if(E.isPlainObject(r[a])&&e!=i)r=r[a];else{if(r[a]!==k)return o=r[a],!1;if(!E.isPlainObject(r[t])||e==i)return r[t]!==k?o=r[t]:v.error(A.method,n),!1;r=r[t]}})),E.isFunction(o)?a=o.apply(t,e):o!==k&&(a=o),E.isArray(l)?l.push(a):l!==k?l=[l,a]:a!==k&&(l=a),o}},F?(c===k&&v.initialize(),v.invoke(f)):(c!==k&&c.invoke("destroy"),v.initialize())}),l!==k?l:this},E.tab=function(){E(O).tab.apply(this,arguments)},E.fn.tab.settings={name:"Tab",namespace:"tab",silent:!1,debug:!1,verbose:!1,performance:!0,auto:!1,history:!1,historyType:"hash",path:!1,context:!1,childrenOnly:!1,maxDepth:25,deactivate:"siblings",alwaysRefresh:!1,cache:!0,loadOnce:!1,cacheType:"response",ignoreFirstLoad:!1,apiSettings:!1,evaluateScripts:"once",onFirstLoad:function(e,t,a){},onLoad:function(e,t,a){},onVisible:function(e,t,a){},onRequest:function(e,t,a){},templates:{determineTitle:function(e){}},error:{api:"You attempted to load content without API module",method:"The method you called is not defined",missingTab:"Activated tab cannot be found. Tabs are case-sensitive.",noContent:"The tab you specified is missing a content url.",path:"History enabled, but no path was specified",recursion:"Max recursive depth reached",legacyInit:"onTabInit has been renamed to onFirstLoad in 2.0, please adjust your code.",legacyLoad:"onTabLoad has been renamed to onLoad in 2.0. Please adjust your code",state:"History requires Asual's Address library <https://github.com/asual/jquery-address>"},metadata:{tab:"tab",loaded:"loaded",promise:"promise"},className:{loading:"loading",active:"active"},selector:{tabs:".vi-ui.tab",ui:".vi-ui"}}}(jQuery,window,document);