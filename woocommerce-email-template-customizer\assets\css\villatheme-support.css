#villatheme-support {
  box-sizing: border-box;
  padding: 30px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}
#villatheme-support * {
  box-sizing: border-box;
}
#villatheme-support a {
  text-decoration: none;
}
#villatheme-support .villatheme-support-head {
  display: flex;
  flex-wrap: wrap;
  align-content: center;
  align-items: center;
  gap: 25px;
}
#villatheme-support .villatheme-support-head .villatheme-support-title {
  font-size: 25px;
  font-weight: 400;
}
#villatheme-support .villatheme-support-head .villatheme-support-action {
  display: flex;
  flex-wrap: wrap;
  align-content: center;
  align-items: center;
  gap: 10px;
}
#villatheme-support .villatheme-support-head .villatheme-support-action .button {
  border-radius: 8px;
  font-size: 13px;
}
#villatheme-support .villatheme-support-head .villatheme-support-action .button.admin-toolbar {
  box-shadow: 0 0 0 1px #00b5ad inset !important;
  color: #00b5ad;
}
#villatheme-support .villatheme-support-head .villatheme-support-action .button.admin-toolbar .icon {
  background: #00b5ad;
  color: #fff;
}
#villatheme-support .villatheme-support-head .villatheme-support-action .button.admin-toolbar:hover {
  background: #00b5ad;
  color: #fff;
}
#villatheme-support .villatheme-support-head .villatheme-support-action .button.support-banner {
  box-shadow: 0 0 0 1px #2ABA4A inset !important;
  color: #2ABA4A;
}
#villatheme-support .villatheme-support-head .villatheme-support-action .button.support-banner .icon {
  background: #2ABA4A;
  color: #fff;
}
#villatheme-support .villatheme-support-head .villatheme-support-action .button.support-banner:hover {
  background: #2ABA4A;
  color: #fff;
}
#villatheme-support .villatheme-support-head .villatheme-support-action .button.review {
  box-shadow: 0 0 0 1px #FC6736 inset !important;
  color: #FC6736;
}
#villatheme-support .villatheme-support-head .villatheme-support-action .button.review .icon {
  background: #FC6736;
  color: #fff;
}
#villatheme-support .villatheme-support-head .villatheme-support-action .button.review:hover {
  background: #FC6736;
  color: #fff;
}
#villatheme-support .villatheme-support-head .villatheme-support-action .button.document {
  box-shadow: 0 0 0 1px #484848 inset !important;
  color: #484848;
}
#villatheme-support .villatheme-support-head .villatheme-support-action .button.document .icon {
  background: #484848;
  color: #fff;
}
#villatheme-support .villatheme-support-head .villatheme-support-action .button.document:hover {
  background: #484848;
  color: #fff;
}
#villatheme-support .villatheme-support-head .villatheme-support-action .button.request-support:hover {
  box-shadow: 0 0 0 1px #21ba45 inset !important;
  color: #21ba45;
  background: #fff;
}
#villatheme-support .villatheme-support-head .villatheme-support-action .button.request-support:hover .icon {
  background: #21ba45;
  color: #fff;
}
#villatheme-support .villatheme-items {
  display: inline-flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
}
#villatheme-support .villatheme-items .villatheme-item {
  flex: 1;
  flex-basis: calc(25% - 20px);
  overflow: hidden;
  border-radius: 6px;
  max-width: 25%;
}
#villatheme-support .villatheme-items .villatheme-item img {
  width: 100%;
  height: 100%;
  transition: 0.25s all ease-in-out;
}
#villatheme-support .villatheme-items .villatheme-item img:hover {
  transform: scale(1.2);
}
#villatheme-support .villatheme-items .villatheme-item:hover {
  box-shadow: 0 0 4px #00000073;
}

.villatheme-dashboard p {
  display: inline-block;
  width: 100%;
}

/*Admin notice*/
.villatheme-dashboard .button {
  padding: 4px 16px;
  color: #393e46;
  border-color: #eee;
  margin-right: 7px;
  z-index: 1 !important;
}

.villatheme-dashboard .button.button-primary {
  color: #fff;
  background: #00adb5;
}

.villatheme-dashboard .button.button-primary:hover {
  border-color: #393e46;
}

.villatheme-dashboard .button.notice-dismiss {
  background: transparent;
  border: none;
  line-height: 19px;
}

.villatheme-dashboard .button.notice-dismiss:hover {
  background: transparent;
}

/*Widget Dashboard*/
.villatheme-dashboard .villatheme-content .villatheme-left {
  width: 100%;
  display: block;
}

.villatheme-dashboard .villatheme-content .villatheme-right, .villatheme-dashboard .villatheme-notification-controls {
  width: 100%;
  display: block;
  text-align: center;
}

/*Extensions page*/
.villatheme-extension-top h2 {
  font-size: 25px;
}

.villatheme-extension-content {
  background: transparent;
  box-sizing: border-box;
  margin: 35px 0;
  border-radius: 20px;
  max-width: 100%;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 35px;
}
.villatheme-extension-content * {
  box-sizing: border-box;
}
.villatheme-extension-content .villatheme-col-3 {
  width: 330px;
  max-width: calc(100% - 80px);
  box-shadow: 0 0 4px #00000026;
  background: #fff;
  color: #000;
  border-radius: 10px;
  font-size: 15px;
  padding: 10px 10px 15px;
  display: inline-flex;
  flex-direction: column;
  justify-content: space-between;
}
.villatheme-extension-content .villatheme-col-3 a {
  text-decoration: none;
  color: #000;
}
.villatheme-extension-content .villatheme-col-3 .villatheme-item-image {
  max-height: 158px;
  overflow: hidden;
}
.villatheme-extension-content .villatheme-col-3 .villatheme-item-image img {
  width: 100%;
  max-height: 100%;
  transition: 0.35s all ease-in-out;
}
.villatheme-extension-content .villatheme-col-3 .villatheme-item-image img:hover {
  transform: scale(1.2);
}
.villatheme-extension-content .villatheme-col-3 .villatheme-item-title {
  font-weight: 500;
  margin: 10px 0 15px 0;
}
.villatheme-extension-content .villatheme-col-3 .villatheme-item-controls .villatheme-item-controls-inner {
  display: inline-flex;
  flex-wrap: wrap;
  gap: 10px;
}
.villatheme-extension-content .villatheme-col-3 .villatheme-item-controls .villatheme-item-controls-inner a.villatheme-item-controls-inner-button {
  padding: 6px 12px;
  border-radius: 8px;
  background: #fff;
  color: #FC6736;
  font-size: 12px;
  line-height: 1;
  box-shadow: 0 0 0 1px #FC6736;
}
.villatheme-extension-content .villatheme-col-3 .villatheme-item-controls .villatheme-item-controls-inner a.villatheme-item-controls-inner-button:hover, .villatheme-extension-content .villatheme-col-3 .villatheme-item-controls .villatheme-item-controls-inner a.villatheme-item-controls-inner-button.active {
  background: #FC6736;
  color: #fff;
}

.villatheme-primary-color {
  color: #f15d58;
  text-decoration: none;
}

.villatheme-content a.vi-button-dismiss:before {
  float: left;
}

.villatheme-content a.vi-button-dismiss {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%) !important;
  padding: 4px 8px;
  font-size: 12px;
  line-height: 16px;
}

.villatheme-button-upgrade {
  color: red;
}

.villatheme-content {
  position: relative;
  padding-right: 86px;
}

.villatheme-item-controls .button {
  padding: 4px 16px;
}

/*Toolbar*/
#wpadminbar > #wp-toolbar > #wp-admin-bar-root-default .villatheme-toolbar .ab-icon {
  font-size: 16px;
  line-height: 1.5;
}

#wpadminbar > #wp-toolbar > #wp-admin-bar-root-default .villatheme-toolbar .ab-icon::before {
  color: #4E9F3D;
}

@-webkit-keyframes villatheme-rotating /* Safari and Chrome */ {
  from {
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes villatheme-rotating {
  from {
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
.villatheme-rotating {
  -webkit-animation: villatheme-rotating 1s ease;
  -moz-animation: villatheme-rotating 1s ease;
  -ms-animation: villatheme-rotating 1s ease;
  -o-animation: villatheme-rotating 1s ease;
  animation: villatheme-rotating 1s ease;
  animation-delay: 2s;
}

#wp-admin-bar-villatheme .dashicons,
#wp-admin-bar-villatheme .dashicons-before:before {
  font-family: "dashicons";
}

#wp-admin-bar-villatheme .villatheme-hide-toolbar-button-title {
  padding: 0 5px;
}

@media screen and (max-width: 480px) {
  .villatheme-content {
    padding-right: 0 !important;
  }

  .villatheme-content a.vi-button-dismiss {
    position: unset;
    transform: unset !important;
  }
}

/*# sourceMappingURL=villatheme-support.css.map */
