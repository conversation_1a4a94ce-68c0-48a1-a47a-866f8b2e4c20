!function(F,A,e,q){"use strict";A=void 0!==A&&A.Math==Math?A:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),F.fn.accordion=function(a){var v,s=F(this),b=(new Date).getTime(),y=[],C=a,O="string"==typeof C,x=[].slice.call(arguments,1);A.requestAnimationFrame||A.mozRequestAnimationFrame||A.webkitRequestAnimationFrame||A.msRequestAnimationFrame;return s.each(function(){var e,c,u=F.isPlainObject(a)?F.extend(!0,{},F.fn.accordion.settings,a):F.extend({},F.fn.accordion.settings),d=u.className,n=u.namespace,g=u.selector,l=u.error,t="."+n,i="module-"+n,o=s.selector||"",f=F(this),m=f.find(g.title),p=f.find(g.content),r=this,h=f.data(i);c={initialize:function(){c.debug("Initializing",f),c.bind.events(),u.observeChanges&&c.observeChanges(),c.instantiate()},instantiate:function(){h=c,f.data(i,c)},destroy:function(){c.debug("Destroying previous instance",f),f.off(t).removeData(i)},refresh:function(){m=f.find(g.title),p=f.find(g.content)},observeChanges:function(){"MutationObserver"in A&&((e=new MutationObserver(function(e){c.debug("DOM tree modified, updating selector cache"),c.refresh()})).observe(r,{childList:!0,subtree:!0}),c.debug("Setting up mutation observer",e))},bind:{events:function(){c.debug("Binding delegated events"),f.on(u.on+t,g.trigger,c.event.click)}},event:{click:function(){c.toggle.call(this)}},toggle:function(e){var n=e!==q?"number"==typeof e?m.eq(e):F(e).closest(g.title):F(this).closest(g.title),t=n.next(p),i=t.hasClass(d.animating),o=t.hasClass(d.active),a=o&&!i,s=!o&&i;c.debug("Toggling visibility of content",n),a||s?u.collapsible?c.close.call(n):c.debug("Cannot close accordion content collapsing is disabled"):c.open.call(n)},open:function(e){var n=e!==q?"number"==typeof e?m.eq(e):F(e).closest(g.title):F(this).closest(g.title),t=n.next(p),i=t.hasClass(d.animating);t.hasClass(d.active)||i?c.debug("Accordion already open, skipping",t):(c.debug("Opening accordion content",n),u.onOpening.call(t),u.onChanging.call(t),u.exclusive&&c.closeOthers.call(n),n.addClass(d.active),t.stop(!0,!0).addClass(d.animating),u.animateChildren&&(F.fn.transition!==q&&f.transition("is supported")?t.children().transition({animation:"fade in",queue:!1,useFailSafe:!0,debug:u.debug,verbose:u.verbose,duration:u.duration}):t.children().stop(!0,!0).animate({opacity:1},u.duration,c.resetOpacity)),t.slideDown(u.duration,u.easing,function(){t.removeClass(d.animating).addClass(d.active),c.reset.display.call(this),u.onOpen.call(this),u.onChange.call(this)}))},close:function(e){var n=e!==q?"number"==typeof e?m.eq(e):F(e).closest(g.title):F(this).closest(g.title),t=n.next(p),i=t.hasClass(d.animating),o=t.hasClass(d.active);!o&&!(!o&&i)||o&&i||(c.debug("Closing accordion content",t),u.onClosing.call(t),u.onChanging.call(t),n.removeClass(d.active),t.stop(!0,!0).addClass(d.animating),u.animateChildren&&(F.fn.transition!==q&&f.transition("is supported")?t.children().transition({animation:"fade out",queue:!1,useFailSafe:!0,debug:u.debug,verbose:u.verbose,duration:u.duration}):t.children().stop(!0,!0).animate({opacity:0},u.duration,c.resetOpacity)),t.slideUp(u.duration,u.easing,function(){t.removeClass(d.animating).removeClass(d.active),c.reset.display.call(this),u.onClose.call(this),u.onChange.call(this)}))},closeOthers:function(e){var n,t,i,o=e!==q?m.eq(e):F(this).closest(g.title),a=o.parents(g.content).prev(g.title),s=o.closest(g.accordion),l=g.title+"."+d.active+":visible",r=g.content+"."+d.active+":visible";i=u.closeNested?(n=s.find(l).not(a)).next(p):(n=s.find(l).not(a),t=s.find(r).find(l).not(a),(n=n.not(t)).next(p)),0<n.length&&(c.debug("Exclusive enabled, closing other content",n),n.removeClass(d.active),i.removeClass(d.animating).stop(!0,!0),u.animateChildren&&(F.fn.transition!==q&&f.transition("is supported")?i.children().transition({animation:"fade out",useFailSafe:!0,debug:u.debug,verbose:u.verbose,duration:u.duration}):i.children().stop(!0,!0).animate({opacity:0},u.duration,c.resetOpacity)),i.slideUp(u.duration,u.easing,function(){F(this).removeClass(d.active),c.reset.display.call(this)}))},reset:{display:function(){c.verbose("Removing inline display from element",this),F(this).css("display",""),""===F(this).attr("style")&&F(this).attr("style","").removeAttr("style")},opacity:function(){c.verbose("Removing inline opacity from element",this),F(this).css("opacity",""),""===F(this).attr("style")&&F(this).attr("style","").removeAttr("style")}},setting:function(e,n){if(c.debug("Changing setting",e,n),F.isPlainObject(e))F.extend(!0,u,e);else{if(n===q)return u[e];F.isPlainObject(u[e])?F.extend(!0,u[e],n):u[e]=n}},internal:function(e,n){if(c.debug("Changing internal",e,n),n===q)return c[e];F.isPlainObject(e)?F.extend(!0,c,e):c[e]=n},debug:function(){!u.silent&&u.debug&&(u.performance?c.performance.log(arguments):(c.debug=Function.prototype.bind.call(console.info,console,u.name+":"),c.debug.apply(console,arguments)))},verbose:function(){!u.silent&&u.verbose&&u.debug&&(u.performance?c.performance.log(arguments):(c.verbose=Function.prototype.bind.call(console.info,console,u.name+":"),c.verbose.apply(console,arguments)))},error:function(){u.silent||(c.error=Function.prototype.bind.call(console.error,console,u.name+":"),c.error.apply(console,arguments))},performance:{log:function(e){var n,t;u.performance&&(t=(n=(new Date).getTime())-(b||n),b=n,y.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:r,"Execution Time":t})),clearTimeout(c.performance.timer),c.performance.timer=setTimeout(c.performance.display,500)},display:function(){var e=u.name+":",t=0;b=!1,clearTimeout(c.performance.timer),F.each(y,function(e,n){t+=n["Execution Time"]}),e+=" "+t+"ms",o&&(e+=" '"+o+"'"),(console.group!==q||console.table!==q)&&0<y.length&&(console.groupCollapsed(e),console.table?console.table(y):F.each(y,function(e,n){console.log(n.Name+": "+n["Execution Time"]+"ms")}),console.groupEnd()),y=[]}},invoke:function(i,e,n){var o,a,t,s=h;return e=e||x,n=r||n,"string"==typeof i&&s!==q&&(i=i.split(/[\. ]/),o=i.length-1,F.each(i,function(e,n){var t=e!=o?n+i[e+1].charAt(0).toUpperCase()+i[e+1].slice(1):i;if(F.isPlainObject(s[t])&&e!=o)s=s[t];else{if(s[t]!==q)return a=s[t],!1;if(!F.isPlainObject(s[n])||e==o)return s[n]!==q?a=s[n]:c.error(l.method,i),!1;s=s[n]}})),F.isFunction(a)?t=a.apply(n,e):a!==q&&(t=a),F.isArray(v)?v.push(t):v!==q?v=[v,t]:t!==q&&(v=t),a}},O?(h===q&&c.initialize(),c.invoke(C)):(h!==q&&h.invoke("destroy"),c.initialize())}),v!==q?v:this},F.fn.accordion.settings={name:"Accordion",namespace:"accordion",silent:!1,debug:!1,verbose:!1,performance:!0,on:"click",observeChanges:!0,exclusive:!0,collapsible:!0,closeNested:!1,animateChildren:!0,duration:350,easing:"easeOutQuad",onOpening:function(){},onClosing:function(){},onChanging:function(){},onOpen:function(){},onClose:function(){},onChange:function(){},error:{method:"The method you called is not defined"},className:{active:"active",animating:"animating"},selector:{accordion:".accordion",title:".title",trigger:".title",content:".content"}},F.extend(F.easing,{easeOutQuad:function(e,n,t,i,o){return-i*(n/=o)*(n-2)+t}})}(jQuery,window,document);